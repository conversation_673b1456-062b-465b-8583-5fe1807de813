import React, { useEffect, useState } from 'react';
import api from '../services/api';

const BerkasList = ({ id }) => {
  const [berkas, setBerkas] = useState(null);

  useEffect(() => {
    const fetchBerkas = async () => {
      try {
        const res = await api.getBerkasSiswa(id);
        setBerkas(res.data.data);
      } catch (err) {
        console.error('Gagal ambil berkas:', err);
      }
    };
    fetchBerkas();
  }, [id]);

  if (!berkas) return <p>Muat berkas...</p>;

  return (
    <div>
      <h3>Berkas Digital</h3>
      <ul>
        {Object.entries(berkas).map(([key, value]) => (
          <li key={key}>
            <strong>{key.replace('_', ' ')}</strong>:{' '}
            {Array.isArray(value) ? (
              <ul>
                {value.map((url, i) => (
                  <li key={i}><a href={url} target="_blank" rel="noopener noreferrer"><PERSON>hat Berkas {i + 1}</a></li>
                ))}
              </ul>
            ) : (
              <a href={value} target="_blank" rel="noopener noreferrer">Lihat Berkas</a>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default BerkasList;