import React from 'react';
import { Link } from 'react-router-dom';
import MainLayout from '../layouts/MainLayout';
import ApiTest from '../common/ApiTest';

const DashboardPage = () => {
  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-2">Dashboard</h2>
          <div className="bg-white rounded-lg shadow-md p-4 sm:p-6">
            <p className="text-base sm:text-lg text-gray-600 mb-2 sm:mb-4">
              Selamat datang di Sistem Informasi Akademik
            </p>
            <p className="text-sm sm:text-base text-gray-500">
              Kelola data siswa, riwayat kelas, dan berkas digital dengan mudah.
            </p>
          </div>
        </div>

        {/* Quick Actions Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 sm:p-6 hover:shadow-md transition-shadow touch-manipulation">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                <span className="text-white text-lg sm:text-xl">👥</span>
              </div>
              <h3 className="text-lg sm:text-xl font-semibold text-blue-800">Data Siswa</h3>
            </div>
            <p className="text-blue-600 mb-4 text-sm sm:text-base">Kelola informasi lengkap siswa</p>
            <Link
              to="/siswa"
              className="inline-block w-full sm:w-auto text-center bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm sm:text-base"
            >
              Lihat Daftar Siswa
            </Link>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4 sm:p-6 hover:shadow-md transition-shadow touch-manipulation">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-600 rounded-lg flex items-center justify-center mr-3">
                <span className="text-white text-lg sm:text-xl">📚</span>
              </div>
              <h3 className="text-lg sm:text-xl font-semibold text-green-800">Riwayat Kelas</h3>
            </div>
            <p className="text-green-600 mb-4 text-sm sm:text-base">Pantau perjalanan akademik siswa</p>
            <Link
              to="/siswa"
              className="inline-block w-full sm:w-auto text-center bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm sm:text-base"
            >
              Lihat Riwayat
            </Link>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 sm:p-6 hover:shadow-md transition-shadow touch-manipulation sm:col-span-2 lg:col-span-1">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-purple-600 rounded-lg flex items-center justify-center mr-3">
                <span className="text-white text-lg sm:text-xl">📁</span>
              </div>
              <h3 className="text-lg sm:text-xl font-semibold text-purple-800">Berkas Digital</h3>
            </div>
            <p className="text-purple-600 mb-4 text-sm sm:text-base">Upload dan kelola dokumen siswa</p>
            <Link
              to="/siswa"
              className="inline-block w-full sm:w-auto text-center bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-sm sm:text-base"
            >
              Kelola Berkas
            </Link>
          </div>
        </div>

        {/* Stats Overview - Mobile Optimized */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6 sm:mb-8">
          <div className="bg-white rounded-lg shadow-md p-3 sm:p-4 text-center">
            <div className="text-xl sm:text-2xl font-bold text-blue-600">150</div>
            <div className="text-xs sm:text-sm text-gray-600">Total Siswa</div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-3 sm:p-4 text-center">
            <div className="text-xl sm:text-2xl font-bold text-green-600">12</div>
            <div className="text-xs sm:text-sm text-gray-600">Kelas Aktif</div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-3 sm:p-4 text-center">
            <div className="text-xl sm:text-2xl font-bold text-purple-600">45</div>
            <div className="text-xs sm:text-sm text-gray-600">Lulus Tahun Ini</div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-3 sm:p-4 text-center">
            <div className="text-xl sm:text-2xl font-bold text-orange-600">8</div>
            <div className="text-xs sm:text-sm text-gray-600">Mutasi</div>
          </div>
        </div>

        {/* API Test Component - Remove in production */}
        <div className="mt-6 sm:mt-8">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <p className="text-yellow-800 text-sm">
              <strong>Development Mode:</strong> API Test component ditampilkan untuk testing.
              Akan dihapus di production.
            </p>
          </div>
          <ApiTest />
        </div>
      </div>
    </MainLayout>
  );
};

export default DashboardPage;