import React from 'react';
import { Link } from 'react-router-dom';
import MainLayout from '../layouts/MainLayout';
import ApiTest from '../common/ApiTest';

const DashboardPage = () => {
  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl font-bold text-gray-800 mb-6">Dashboard</h2>
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <p className="text-lg text-gray-600 mb-4">
            Selamat datang di Sistem Informasi Akademik
          </p>
          <p className="text-gray-500 mb-6">
            Kelola data siswa, riwayat kelas, dan berkas digital dengan mudah.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 hover:shadow-md transition-shadow">
            <h3 className="text-xl font-semibold text-blue-800 mb-3">Data Siswa</h3>
            <p className="text-blue-600 mb-4">Kelola informasi lengkap siswa</p>
            <Link
              to="/siswa"
              className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            >
              Lihat Daftar Siswa
            </Link>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-6 hover:shadow-md transition-shadow">
            <h3 className="text-xl font-semibold text-green-800 mb-3">Riwayat Kelas</h3>
            <p className="text-green-600 mb-4">Pantau perjalanan akademik siswa</p>
            <Link
              to="/siswa"
              className="inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors"
            >
              Lihat Riwayat
            </Link>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 hover:shadow-md transition-shadow">
            <h3 className="text-xl font-semibold text-purple-800 mb-3">Berkas Digital</h3>
            <p className="text-purple-600 mb-4">Upload dan kelola dokumen siswa</p>
            <Link
              to="/siswa"
              className="inline-block bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
            >
              Kelola Berkas
            </Link>
          </div>
        </div>

        {/* API Test Component - Remove in production */}
        <div className="mt-8">
          <ApiTest />
        </div>
      </div>
    </MainLayout>
  );
};

export default DashboardPage;