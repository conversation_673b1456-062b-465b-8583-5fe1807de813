import axios from 'axios';

const apiClient = axios.create({
  baseURL: 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth token if needed
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

const api = {
  // Auth endpoints
  post: (url, data) => apiClient.post(url, data),
  get: (url, config) => apiClient.get(url, config),
  put: (url, data) => apiClient.put(url, data),
  delete: (url) => apiClient.delete(url),

  // Siswa endpoints
  getSiswaList(params = {}) {
    return apiClient.get('/siswa', { params });
  },

  getSiswaById(id) {
    return apiClient.get(`/siswa/${id}`);
  },

  createSiswa(data) {
    return apiClient.post('/siswa', data);
  },

  updateSiswa(id, data) {
    return apiClient.put(`/siswa/${id}`, data);
  },

  deleteSiswa(id) {
    return apiClient.delete(`/siswa/${id}`);
  },

  // Riwayat Kelas endpoints
  getRiwayatKelas(id) {
    return apiClient.get(`/siswa/${id}/riwayat_kelas`);
  },

  addRiwayatKelas(id, data) {
    return apiClient.post(`/siswa/${id}/riwayat_kelas`, data);
  },

  updateRiwayatKelas(id, riwayatId, data) {
    return apiClient.put(`/siswa/${id}/riwayat_kelas/${riwayatId}`, data);
  },

  // Berkas endpoints
  getBerkasList(id) {
    return apiClient.get(`/siswa/${id}/berkas`);
  },

  uploadBerkas(formData) {
    return apiClient.post('/berkas/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
        // You can use this for progress tracking
        console.log(`Upload Progress: ${percentCompleted}%`);
      },
    });
  },

  deleteBerkas(berkasId) {
    return apiClient.delete(`/berkas/${berkasId}`);
  },

  downloadBerkas(berkasId) {
    return apiClient.get(`/berkas/${berkasId}/download`, {
      responseType: 'blob',
    });
  },

  // Dashboard/Statistics endpoints
  getDashboardStats() {
    return apiClient.get('/dashboard/stats');
  },

  getKelasStats() {
    return apiClient.get('/dashboard/kelas-stats');
  },

  // Search endpoints
  searchSiswa(query) {
    return apiClient.get('/siswa/search', {
      params: { q: query }
    });
  },

  // Mutasi endpoints
  createMutasi(data) {
    return apiClient.post('/mutasi', data);
  },

  getMutasiList(params = {}) {
    return apiClient.get('/mutasi', { params });
  },

  // Kelulusan endpoints
  createKelulusan(data) {
    return apiClient.post('/kelulusan', data);
  },

  getKelulusanList(params = {}) {
    return apiClient.get('/kelulusan', { params });
  },
};