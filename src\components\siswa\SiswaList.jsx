import React, { useEffect, useState } from 'react';
import api from '../services/api';

const SiswaList = () => {
  const [siswaList, setSiswaList] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSiswa = async () => {
      try {
        const res = await api.getSiswaList();
        setSiswaList(res.data.data);
        setLoading(false);
      } catch (err) {
        console.error('Gagal ambil data siswa', err);
        setLoading(false);
      }
    };
    fetchSiswa();
  }, []);

  if (loading) return <p>Loading...</p>;

  return (
    <div>
      <h2>Daftar Siswa</h2>
      <ul>
        {siswaList.map((siswa) => (
          <li key={siswa.id_siswa}>
            <a href={`#${siswa.id_siswa}`} onClick={(e) => alert(`Lihat detail ${siswa.id_siswa}`)}>
              {siswa.nama_lengkap} ({siswa.nis})
            </a>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default SiswaList;