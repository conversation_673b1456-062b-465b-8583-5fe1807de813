import React from 'react';
import FileUpload from '../common/FileUpload';

const UploadBerkasPage = () => {
  const idSiswa = 'SIS001'; // Bisa ambil dari useParams jika pakai routing

  const handleUpload = (data) => {
    console.log('Upload sukses!', data);
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-6">Upload Berkas Siswa</h2>

      <FileUpload
        id_siswa={idSiswa}
        jenis_berkas="kartu_keluarga"
        label="Upload Kartu Keluarga"
        onUploadSuccess={handleUpload}
      />

      <FileUpload
        id_siswa={idSiswa}
        jenis_berkas="akta_lahir"
        label="Upload Akta Lahir"
        onUploadSuccess={handleUpload}
      />

      <FileUpload
        id_siswa={idSiswa}
        jenis_berkas="rapor"
        label="Upload Rapor"
        onUploadSuccess={handleUpload}
      />

      <FileUpload
        id_siswa={idSiswa}
        jenis_berkas="ijazah_sebelumnya"
        label="Upload Ijazah Sebelumnya"
        onUploadSuccess={handleUpload}
      />
    </div>
  );
};

export default UploadBerkasPage;