import React, { useState } from 'react';
import axios from 'axios';

/**
 * Komponen reusable untuk upload berkas siswa
 * @param {string} id_siswa - ID siswa
 * @param {string} jenis_berkas - <PERSON><PERSON> berka<PERSON> (rapor, kartu_keluarga, dll.)
 * @param {string} label - Label tampilan untuk pengguna
 * @param {function} onUploadSuccess - Callback setelah upload berhasil
 */
const FileUpload = ({ id_siswa, jenis_berkas, label, onUploadSuccess }) => {
  const [file, setFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
    setError('');
    setSuccessMessage('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!file) {
      setError('Silakan pilih file terlebih dahulu.');
      return;
    }

    setUploading(true);

    const formData = new FormData();
    formData.append(jenis_berkas, file); // sesuai endpoint backend

    try {
      const response = await axios.post(
        `http://localhost:5000/api/siswa/${id_siswa}/upload_berkas`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      if (response.status === 200) {
        setSuccessMessage('Berkas berhasil diupload!');
        setError('');
        if (onUploadSuccess) onUploadSuccess(response.data);
        setFile(null);
      }
    } catch (err) {
      console.error('Upload error:', err);
      setError('Gagal mengupload berkas. Silakan coba lagi.');
      setSuccessMessage('');
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="mb-6">
      <label className="block text-lg font-medium mb-2">{label}</label>

      <form onSubmit={handleSubmit} className="space-y-3">
        <input
          type="file"
          onChange={handleFileChange}
          className="block w-full text-sm text-gray-500 border border-gray-300 rounded-md cursor-pointer bg-gray-50 focus:outline-none"
        />

        {file && <p className="text-sm text-gray-600">Dipilih: {file.name}</p>}

        {error && <p className="text-red-500 text-sm">{error}</p>}
        {successMessage && <p className="text-green-600 text-sm">{successMessage}</p>}

        <button
          type="submit"
          disabled={uploading}
          className={`px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition ${
            uploading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {uploading ? 'Mengupload...' : 'Upload Berkas'}
        </button>
      </form>
    </div>
  );
};

export default FileUpload;