import React from 'react';
import { Routes, Route } from 'react-router-dom';

// Halaman
import DashboardPage from '../components/pages/DashboardPage';
import SiswaListPage from '../components/pages/SiswaListPage';
import SiswaDetailPage from '../components/pages/SiswaDetailPage';
import UploadBerkasPage from '../components/pages/UploadBerkasPage';

// Layout
import MainLayout from '../components/layouts/MainLayout';

/**
 * Komponen AppRoutes - Mengatur semua route aplikasi
 */
const AppRoutes = () => {
  return (
    <Routes>
      {/* Root route */}
      <Route path="/" element={<MainLayout />}>
        <Route index element={<DashboardPage />} />
        
        {/* Modul Siswa */}
        <Route path="siswa" element={<SiswaListPage />} />
        <Route path="siswa/:id" element={<SiswaDetailPage />} />
        <Route path="upload-berkas/:id" element={<UploadBerkasPage />} />

        {/* Jika route tidak ditemukan */}
        <Route path="*" element={<p>Halaman Tidak Ditemukan</p>} />
      </Route>
    </Routes>
  );
};

export default AppRoutes;