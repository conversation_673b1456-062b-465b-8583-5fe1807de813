<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sistem Informasi Akademik Siswa</title>
    <meta name="description" content="Aplikasi manajemen data siswa lengkap dengan riwayat kelas, kelulusan, mutasi, dan berkas digital." />
    <meta name="keywords" content="siswa, akademik, sekolah, manajemen data, pendidikan" />
    <meta name="author" content="Sistem Informasi Siswa" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link 
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" 
      rel="stylesheet" 
    />
    
    <!-- Meta tags for SEO and social sharing -->
    <meta property="og:title" content="Sistem Informasi Akademik Siswa" />
    <meta property="og:description" content="Aplikasi manajemen data siswa lengkap dengan riwayat kelas, kelulusan, mutasi, dan berkas digital." />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    
    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#2563eb" />
    
    <style>
      /* Prevent flash of unstyled content */
      body {
        font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f9fafb;
      }
      
      /* Loading screen */
      #initial-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      
      .loader-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #2563eb;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
    <script type="module" crossorigin src="/assets/index-CzuBd4z7.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-Gm9i_4Ku.js">
    <link rel="modulepreload" crossorigin href="/assets/router-DBIF4lX1.js">
    <link rel="modulepreload" crossorigin href="/assets/utils-xsH4HHeE.js">
    <link rel="stylesheet" crossorigin href="/assets/index-C2Z6l0nY.css">
  </head>
  <body>
    <!-- Initial loading screen -->
    <div id="initial-loader">
      <div class="loader-spinner"></div>
    </div>
    
    <div id="root"></div>
    
    
    <script>
      // Hide loader when React app is ready
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loader = document.getElementById('initial-loader');
          if (loader) {
            loader.style.opacity = '0';
            loader.style.transition = 'opacity 0.3s ease';
            setTimeout(() => loader.remove(), 300);
          }
        }, 500);
      });
    </script>
  </body>
</html>
