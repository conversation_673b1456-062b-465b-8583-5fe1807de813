import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import api from '../../services/api';
import MainLayout from '../layouts/MainLayout';

const SiswaDetailPage = () => {
  const { id } = useParams();
  const [siswa, setSiswa] = useState(null);
  const [kelasHistory, setKelasHistory] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const siswaRes = await api.getSiswaById(id);
        const kelasRes = await api.getRiwayatKelas(id);
        setSiswa(siswaRes.data.data);
        setKelasHistory(kelasRes.data.data);
      } catch (err) {
        alert('Gagal memuat detail siswa');
      }
    };

    if (id) fetchData();
  }, [id]);

  if (!siswa) return <MainLayout>Muat detail...</MainLayout>;

  return (
    <MainLayout>
      <h2>{siswa.nama_lengkap}</h2>
      <p><strong>NIS:</strong> {siswa.nis}</p>
      <p><strong>Alamat:</strong> {siswa.alamat}</p>

      <h3>Riwayat Kelas</h3>
      <ul>
        {kelasHistory.map((item, idx) => (
          <li key={idx}>
            {item.tahun_pelajaran}: {item.kelas_awal} → {item.kelas_naik}
            <br />
            <small>Status: {item.status_naik ? 'Naik' : 'Tidak Naik'} - {item.catatan}</small>
          </li>
        ))}
      </ul>
    </MainLayout>
  );
};

export default SiswaDetailPage;