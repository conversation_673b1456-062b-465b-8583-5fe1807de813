import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/common/ProtectedRoute';
import LoginPage from './components/pages/LoginPage';
import DashboardPage from './components/pages/DashboardPage';
import SiswaListPage from './components/pages/SiswaListPage';
import SiswaDetailPage from './components/pages/SiswaDetailPage';
import UploadBerkasPage from './components/pages/UploadBerkasPage';

function App() {
  return (
    <Router>
      <AuthProvider>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <DashboardPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/siswa"
            element={
              <ProtectedRoute>
                <SiswaListPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/siswa/:id"
            element={
              <ProtectedRoute>
                <SiswaDetailPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/upload-berkas/:id"
            element={
              <ProtectedRoute roles={['admin', 'staff']}>
                <UploadBerkasPage />
              </ProtectedRoute>
            }
          />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </AuthProvider>
    </Router>
  );
}

export default App;