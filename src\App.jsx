import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import DashboardPage from './components/pages/DashboardPage';
import SiswaListPage from './components/pages/SiswaListPage';
import SiswaDetailPage from './components/pages/SiswaDetailPage';
import UploadBerkasPage from './components/pages/UploadBerkasPage';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<DashboardPage />} />
        <Route path="/siswa" element={<SiswaListPage />} />
        <Route path="/siswa/:id" element={<SiswaDetailPage />} />
        <Route path="/upload-berkas/:id" element={<UploadBerkasPage />} />
      </Routes>
    </Router>
  );
}

export default App;