import React, { useEffect, useState } from 'react';
import api from '../../services/api';
import MainLayout from '../layouts/MainLayout';

const SiswaListPage = () => {
  const [siswaList, setSiswaList] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSiswa = async () => {
      try {
        const res = await api.getSiswaList();
        setSiswaList(res.data.data);
        setLoading(false);
      } catch (err) {
        alert('Gagal memuat data siswa');
        setLoading(false);
      }
    };
    fetchSiswa();
  }, []);

  if (loading) return <MainLayout>Loading...</MainLayout>;

  return (
    <MainLayout>
      <h2>Daftar Siswa</h2>
      <table border="1" cellPadding="10">
        <thead>
          <tr>
            <th>Nama</th>
            <th>NIS</th>
            <th><PERSON><PERSON></th>
            <th>Aksi</th>
          </tr>
        </thead>
        <tbody>
          {siswaList.map((s) => (
            <tr key={s.id_siswa}>
              <td>{s.nama_lengkap}</td>
              <td>{s.nis}</td>
              <td>{s.kelas_awal || '-'}</td>
              <td>
                <a href={`/siswa/${s.id_siswa}`}>Detail</a>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </MainLayout>
  );
};

export default SiswaListPage;