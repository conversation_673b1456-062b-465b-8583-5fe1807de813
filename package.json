{"name": "siswa-app", "version": "1.0.0", "description": "Aplikasi Manajemen Data Siswa Lengkap dengan Riwayat Kelas, Berkas Digital, Mutasi & Kelulusan", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.6.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^5.0.0"}, "keywords": ["react", "siswa", "manajemen-data", "akademik", "school-system"], "author": "<PERSON><PERSON>", "license": "MIT"}