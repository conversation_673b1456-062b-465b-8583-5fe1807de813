import React, { useEffect, useState } from 'react';
import api from '../../services/api';
import DataCard from '../common/DataCard';

const SiswaDetail = ({ id }) => {
  const [siswa, setSiswa] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await api.getSiswaById(id);
        setSiswa(res.data.data);
      } catch (err) {
        console.error('Gagal mengambil detail siswa', err);
      }
    };
    fetchData();
  }, [id]);

  if (!siswa) return <p>Memuat data...</p>;

  return (
    <DataCard title="Identitas Siswa">
      <p><strong>Nama:</strong> {siswa.nama_lengkap}</p>
      <p><strong>NIS:</strong> {siswa.nis}</p>
      <p><strong>NIK:</strong> {siswa.nik}</p>
      <p><strong>Alamat:</strong> {siswa.alamat}</p>
      <p><strong><PERSON>a <PERSON>yah:</strong> {siswa.nama_ayah}</p>
      <p><strong>Nama Ibu:</strong> {siswa.nama_ibu}</p>
    </DataCard>
  );
};

export default SiswaDetail;