{"version": 3, "file": "index-CzuBd4z7.js", "sources": ["../../node_modules/react/cjs/react-jsx-runtime.production.min.js", "../../node_modules/react/jsx-runtime.js", "../../node_modules/react-dom/client.js", "../../src/components/layouts/MainLayout.jsx", "../../src/components/pages/DashboardPage.jsx", "../../src/services/api.js", "../../src/components/pages/SiswaListPage.jsx", "../../src/components/pages/SiswaDetailPage.jsx", "../../src/components/pages/UploadBerkasPage.jsx", "../../src/App.jsx", "../../src/main.jsx"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var f=require(\"react\"),k=Symbol.for(\"react.element\"),l=Symbol.for(\"react.fragment\"),m=Object.prototype.hasOwnProperty,n=f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,p={key:!0,ref:!0,__self:!0,__source:!0};\nfunction q(c,a,g){var b,d={},e=null,h=null;void 0!==g&&(e=\"\"+g);void 0!==a.key&&(e=\"\"+a.key);void 0!==a.ref&&(h=a.ref);for(b in a)m.call(a,b)&&!p.hasOwnProperty(b)&&(d[b]=a[b]);if(c&&c.defaultProps)for(b in a=c.defaultProps,a)void 0===d[b]&&(d[b]=a[b]);return{$$typeof:k,type:c,key:e,ref:h,props:d,_owner:n.current}}exports.Fragment=l;exports.jsx=q;exports.jsxs=q;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n", "import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\n\r\nconst MainLayout = ({ children }) => {\r\n  const location = useLocation();\r\n\r\n  const isActive = (path) => {\r\n    return location.pathname === path;\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex justify-between items-center py-4\">\r\n            <div className=\"flex items-center\">\r\n              <h1 className=\"text-2xl font-bold text-gray-900\">\r\n                Sistem Informasi Akademik\r\n              </h1>\r\n            </div>\r\n            <nav className=\"flex space-x-8\">\r\n              <Link\r\n                to=\"/\"\r\n                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${\r\n                  isActive('/')\r\n                    ? 'bg-blue-100 text-blue-700'\r\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\r\n                }`}\r\n              >\r\n                Dashboard\r\n              </Link>\r\n              <Link\r\n                to=\"/siswa\"\r\n                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${\r\n                  isActive('/siswa')\r\n                    ? 'bg-blue-100 text-blue-700'\r\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\r\n                }`}\r\n              >\r\n                Data Siswa\r\n              </Link>\r\n            </nav>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      <main className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\r\n        {children}\r\n      </main>\r\n\r\n      <footer className=\"bg-white border-t border-gray-200 mt-auto\">\r\n        <div className=\"max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8\">\r\n          <p className=\"text-center text-sm text-gray-500\">\r\n            © 2025 Sistem Informasi Siswa. All rights reserved.\r\n          </p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MainLayout;", "import React from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport MainLayout from '../layouts/MainLayout';\r\n\r\nconst DashboardPage = () => {\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"max-w-4xl mx-auto\">\r\n        <h2 className=\"text-3xl font-bold text-gray-800 mb-6\">Dashboard</h2>\r\n        <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\r\n          <p className=\"text-lg text-gray-600 mb-4\">\r\n            Selamat datang di Sistem Informasi Akademik\r\n          </p>\r\n          <p className=\"text-gray-500 mb-6\">\r\n            Kelola data siswa, riwayat kelas, dan berkas digital dengan mudah.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6 hover:shadow-md transition-shadow\">\r\n            <h3 className=\"text-xl font-semibold text-blue-800 mb-3\">Data Siswa</h3>\r\n            <p className=\"text-blue-600 mb-4\">Kelola informasi lengkap siswa</p>\r\n            <Link\r\n              to=\"/siswa\"\r\n              className=\"inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors\"\r\n            >\r\n              Lihat Daftar Siswa\r\n            </Link>\r\n          </div>\r\n\r\n          <div className=\"bg-green-50 border border-green-200 rounded-lg p-6 hover:shadow-md transition-shadow\">\r\n            <h3 className=\"text-xl font-semibold text-green-800 mb-3\">Riwayat Kelas</h3>\r\n            <p className=\"text-green-600 mb-4\">Pantau perjalanan akademik siswa</p>\r\n            <Link\r\n              to=\"/siswa\"\r\n              className=\"inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors\"\r\n            >\r\n              Lihat Riwayat\r\n            </Link>\r\n          </div>\r\n\r\n          <div className=\"bg-purple-50 border border-purple-200 rounded-lg p-6 hover:shadow-md transition-shadow\">\r\n            <h3 className=\"text-xl font-semibold text-purple-800 mb-3\">Berkas Digital</h3>\r\n            <p className=\"text-purple-600 mb-4\">Upload dan kelola dokumen siswa</p>\r\n            <Link\r\n              to=\"/siswa\"\r\n              className=\"inline-block bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors\"\r\n            >\r\n              Kelola Berkas\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </MainLayout>\r\n  );\r\n};\r\n\r\nexport default DashboardPage;", "import axios from 'axios';\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: 'http://localhost:5000/api',\r\n  timeout: 10000,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Request interceptor for adding auth token if needed\r\napiClient.interceptors.request.use(\r\n  (config) => {\r\n    // Add auth token if available\r\n    const token = localStorage.getItem('authToken');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor for handling errors\r\napiClient.interceptors.response.use(\r\n  (response) => {\r\n    return response;\r\n  },\r\n  (error) => {\r\n    if (error.response?.status === 401) {\r\n      // Handle unauthorized access\r\n      localStorage.removeItem('authToken');\r\n      window.location.href = '/login';\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default {\r\n  // Siswa endpoints\r\n  getSiswaList(params = {}) {\r\n    return apiClient.get('/siswa', { params });\r\n  },\r\n\r\n  getSiswaById(id) {\r\n    return apiClient.get(`/siswa/${id}`);\r\n  },\r\n\r\n  createSiswa(data) {\r\n    return apiClient.post('/siswa', data);\r\n  },\r\n\r\n  updateSiswa(id, data) {\r\n    return apiClient.put(`/siswa/${id}`, data);\r\n  },\r\n\r\n  deleteSiswa(id) {\r\n    return apiClient.delete(`/siswa/${id}`);\r\n  },\r\n\r\n  // Riwayat Kelas endpoints\r\n  getRiwayatKelas(id) {\r\n    return apiClient.get(`/siswa/${id}/riwayat_kelas`);\r\n  },\r\n\r\n  addRiwayatKelas(id, data) {\r\n    return apiClient.post(`/siswa/${id}/riwayat_kelas`, data);\r\n  },\r\n\r\n  updateRiwayatKelas(id, riwayatId, data) {\r\n    return apiClient.put(`/siswa/${id}/riwayat_kelas/${riwayatId}`, data);\r\n  },\r\n\r\n  // Berkas endpoints\r\n  getBerkasList(id) {\r\n    return apiClient.get(`/siswa/${id}/berkas`);\r\n  },\r\n\r\n  uploadBerkas(formData) {\r\n    return apiClient.post('/berkas/upload', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n      onUploadProgress: (progressEvent) => {\r\n        const percentCompleted = Math.round(\r\n          (progressEvent.loaded * 100) / progressEvent.total\r\n        );\r\n        // You can use this for progress tracking\r\n        console.log(`Upload Progress: ${percentCompleted}%`);\r\n      },\r\n    });\r\n  },\r\n\r\n  deleteBerkas(berkasId) {\r\n    return apiClient.delete(`/berkas/${berkasId}`);\r\n  },\r\n\r\n  downloadBerkas(berkasId) {\r\n    return apiClient.get(`/berkas/${berkasId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n  },\r\n\r\n  // Dashboard/Statistics endpoints\r\n  getDashboardStats() {\r\n    return apiClient.get('/dashboard/stats');\r\n  },\r\n\r\n  getKelasStats() {\r\n    return apiClient.get('/dashboard/kelas-stats');\r\n  },\r\n\r\n  // Search endpoints\r\n  searchSiswa(query) {\r\n    return apiClient.get('/siswa/search', {\r\n      params: { q: query }\r\n    });\r\n  },\r\n\r\n  // Mutasi endpoints\r\n  createMutasi(data) {\r\n    return apiClient.post('/mutasi', data);\r\n  },\r\n\r\n  getMutasiList(params = {}) {\r\n    return apiClient.get('/mutasi', { params });\r\n  },\r\n\r\n  // Kelulusan endpoints\r\n  createKelulusan(data) {\r\n    return apiClient.post('/kelulusan', data);\r\n  },\r\n\r\n  getKelulusanList(params = {}) {\r\n    return apiClient.get('/kelulusan', { params });\r\n  },\r\n};", "import React, { useEffect, useState } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport api from '../../services/api';\r\nimport MainLayout from '../layouts/MainLayout';\r\n\r\nconst SiswaListPage = () => {\r\n  const [siswaList, setSiswaList] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const fetchSiswa = async () => {\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n        const res = await api.getSiswaList();\r\n        setSiswaList(res.data.data || []);\r\n      } catch (err) {\r\n        console.error('Error fetching siswa:', err);\r\n        setError('Gagal memuat data siswa. Silakan coba lagi.');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchSiswa();\r\n  }, []);\r\n\r\n  if (loading) {\r\n    return (\r\n      <MainLayout>\r\n        <div className=\"flex justify-center items-center py-12\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\r\n          <span className=\"ml-3 text-gray-600\">Memuat data siswa...</span>\r\n        </div>\r\n      </MainLayout>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <MainLayout>\r\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-sm font-medium text-red-800\">Error</h3>\r\n              <p className=\"text-sm text-red-700 mt-1\">{error}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </MainLayout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"max-w-6xl mx-auto\">\r\n        <div className=\"flex justify-between items-center mb-6\">\r\n          <h2 className=\"text-3xl font-bold text-gray-800\">Daftar Siswa</h2>\r\n          <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\r\n            + Tambah Siswa\r\n          </button>\r\n        </div>\r\n\r\n        {siswaList.length === 0 ? (\r\n          <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-8 text-center\">\r\n            <p className=\"text-gray-500 text-lg\">Belum ada data siswa</p>\r\n            <p className=\"text-gray-400 mt-2\">Tambahkan siswa pertama untuk memulai</p>\r\n          </div>\r\n        ) : (\r\n          <div className=\"bg-white shadow-md rounded-lg overflow-hidden\">\r\n            <div className=\"overflow-x-auto\">\r\n              <table className=\"min-w-full divide-y divide-gray-200\">\r\n                <thead className=\"bg-gray-50\">\r\n                  <tr>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      Nama Lengkap\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      NIS\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      Kelas\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                      Aksi\r\n                    </th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                  {siswaList.map((siswa) => (\r\n                    <tr key={siswa.id_siswa} className=\"hover:bg-gray-50\">\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"text-sm font-medium text-gray-900\">\r\n                          {siswa.nama_lengkap}\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"text-sm text-gray-900\">{siswa.nis}</div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">\r\n                          {siswa.kelas_awal || '-'}\r\n                        </span>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n                        <Link\r\n                          to={`/siswa/${siswa.id_siswa}`}\r\n                          className=\"text-blue-600 hover:text-blue-900 mr-4\"\r\n                        >\r\n                          Detail\r\n                        </Link>\r\n                        <Link\r\n                          to={`/upload-berkas/${siswa.id_siswa}`}\r\n                          className=\"text-green-600 hover:text-green-900\"\r\n                        >\r\n                          Upload Berkas\r\n                        </Link>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </MainLayout>\r\n  );\r\n};\r\n\r\nexport default SiswaListPage;", "import React, { useEffect, useState } from 'react';\r\nimport { usePara<PERSON>, Link } from 'react-router-dom';\r\nimport api from '../../services/api';\r\nimport MainLayout from '../layouts/MainLayout';\r\n\r\nconst SiswaDetailPage = () => {\r\n  const { id } = useParams();\r\n  const [siswa, setSiswa] = useState(null);\r\n  const [kelasHistory, setKelasHistory] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n        const [siswaRes, kelasRes] = await Promise.all([\r\n          api.getSiswaById(id),\r\n          api.getRiwayatKelas(id)\r\n        ]);\r\n        setSiswa(siswaRes.data.data);\r\n        setKelasHistory(kelasRes.data.data || []);\r\n      } catch (err) {\r\n        console.error('Error fetching siswa detail:', err);\r\n        setError('Gagal memuat detail siswa. Silakan coba lagi.');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (id) fetchData();\r\n  }, [id]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <MainLayout>\r\n        <div className=\"flex justify-center items-center py-12\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\r\n          <span className=\"ml-3 text-gray-600\">Memuat detail siswa...</span>\r\n        </div>\r\n      </MainLayout>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <MainLayout>\r\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-sm font-medium text-red-800\">Error</h3>\r\n              <p className=\"text-sm text-red-700 mt-1\">{error}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </MainLayout>\r\n    );\r\n  }\r\n\r\n  if (!siswa) {\r\n    return (\r\n      <MainLayout>\r\n        <div className=\"text-center py-12\">\r\n          <p className=\"text-gray-500\">Data siswa tidak ditemukan</p>\r\n          <Link to=\"/siswa\" className=\"text-blue-600 hover:text-blue-800 mt-2 inline-block\">\r\n            Kembali ke Daftar Siswa\r\n          </Link>\r\n        </div>\r\n      </MainLayout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"max-w-4xl mx-auto\">\r\n        {/* Header */}\r\n        <div className=\"flex justify-between items-center mb-6\">\r\n          <div>\r\n            <Link to=\"/siswa\" className=\"text-blue-600 hover:text-blue-800 text-sm mb-2 inline-block\">\r\n              ← Kembali ke Daftar Siswa\r\n            </Link>\r\n            <h2 className=\"text-3xl font-bold text-gray-800\">{siswa.nama_lengkap}</h2>\r\n          </div>\r\n          <div className=\"flex space-x-3\">\r\n            <Link\r\n              to={`/upload-berkas/${id}`}\r\n              className=\"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\"\r\n            >\r\n              Upload Berkas\r\n            </Link>\r\n            <button className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\r\n              Edit Data\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n          {/* Informasi Siswa */}\r\n          <div className=\"lg:col-span-2\">\r\n            <div className=\"bg-white rounded-lg shadow-md p-6 mb-6\">\r\n              <h3 className=\"text-xl font-semibold text-gray-800 mb-4\">Informasi Siswa</h3>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-500\">NIS</label>\r\n                  <p className=\"text-lg text-gray-900\">{siswa.nis}</p>\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-500\">Nama Lengkap</label>\r\n                  <p className=\"text-lg text-gray-900\">{siswa.nama_lengkap}</p>\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-500\">Kelas Awal</label>\r\n                  <p className=\"text-lg text-gray-900\">{siswa.kelas_awal || '-'}</p>\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-500\">Status</label>\r\n                  <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">\r\n                    Aktif\r\n                  </span>\r\n                </div>\r\n                <div className=\"md:col-span-2\">\r\n                  <label className=\"block text-sm font-medium text-gray-500\">Alamat</label>\r\n                  <p className=\"text-lg text-gray-900\">{siswa.alamat || 'Belum diisi'}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Riwayat Kelas */}\r\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\r\n              <h3 className=\"text-xl font-semibold text-gray-800 mb-4\">Riwayat Kelas</h3>\r\n              {kelasHistory.length === 0 ? (\r\n                <div className=\"text-center py-8\">\r\n                  <p className=\"text-gray-500\">Belum ada riwayat kelas</p>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-4\">\r\n                  {kelasHistory.map((item, idx) => (\r\n                    <div key={idx} className=\"border border-gray-200 rounded-lg p-4\">\r\n                      <div className=\"flex justify-between items-start\">\r\n                        <div>\r\n                          <h4 className=\"font-semibold text-gray-800\">\r\n                            Tahun Pelajaran {item.tahun_pelajaran}\r\n                          </h4>\r\n                          <p className=\"text-gray-600 mt-1\">\r\n                            {item.kelas_awal} → {item.kelas_naik}\r\n                          </p>\r\n                          {item.catatan && (\r\n                            <p className=\"text-sm text-gray-500 mt-2\">{item.catatan}</p>\r\n                          )}\r\n                        </div>\r\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\r\n                          item.status_naik\r\n                            ? 'bg-green-100 text-green-800'\r\n                            : 'bg-red-100 text-red-800'\r\n                        }`}>\r\n                          {item.status_naik ? 'Naik Kelas' : 'Tidak Naik'}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Sidebar */}\r\n          <div className=\"lg:col-span-1\">\r\n            <div className=\"bg-white rounded-lg shadow-md p-6\">\r\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Aksi Cepat</h3>\r\n              <div className=\"space-y-3\">\r\n                <Link\r\n                  to={`/upload-berkas/${id}`}\r\n                  className=\"block w-full text-center bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\"\r\n                >\r\n                  Upload Berkas\r\n                </Link>\r\n                <button className=\"block w-full text-center bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\r\n                  Edit Data Siswa\r\n                </button>\r\n                <button className=\"block w-full text-center bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors\">\r\n                  Lihat Berkas\r\n                </button>\r\n                <button className=\"block w-full text-center bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors\">\r\n                  Proses Mutasi\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </MainLayout>\r\n  );\r\n};\r\n\r\nexport default SiswaDetailPage;", "import React, { useState, useEffect } from 'react';\r\nimport { usePara<PERSON>, Link } from 'react-router-dom';\r\nimport MainLayout from '../layouts/MainLayout';\r\nimport api from '../../services/api';\r\n\r\nconst UploadBerkasPage = () => {\r\n  const { id } = useParams();\r\n  const [siswa, setSiswa] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [uploadProgress, setUploadProgress] = useState({});\r\n  const [uploadedFiles, setUploadedFiles] = useState({});\r\n\r\n  const berkasTypes = [\r\n    { key: 'kartu_keluarga', label: 'Kartu Keluarga', icon: '👨‍👩‍👧‍👦' },\r\n    { key: 'akta_lahir', label: 'Akta Lahir', icon: '📄' },\r\n    { key: 'rapor', label: 'Rapor', icon: '📊' },\r\n    { key: 'ijazah_sebelumnya', label: 'Ijazah <PERSON>bel<PERSON>', icon: '🎓' },\r\n    { key: 'foto', label: 'Foto Siswa', icon: '📸' },\r\n    { key: 'surat_keterangan_sehat', label: 'Surat Keterangan Sehat', icon: '🏥' }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const fetchSiswa = async () => {\r\n      try {\r\n        const res = await api.getSiswaById(id);\r\n        setSiswa(res.data.data);\r\n      } catch (err) {\r\n        console.error('Error fetching siswa:', err);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (id) fetchSiswa();\r\n  }, [id]);\r\n\r\n  const handleFileUpload = async (file, jenisberkas) => {\r\n    if (!file) return;\r\n\r\n    // Simulate upload progress\r\n    setUploadProgress(prev => ({ ...prev, [jenisberkas]: 0 }));\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('id_siswa', id);\r\n    formData.append('jenis_berkas', jenisberkas);\r\n\r\n    try {\r\n      // Simulate progress\r\n      for (let i = 0; i <= 100; i += 10) {\r\n        await new Promise(resolve => setTimeout(resolve, 100));\r\n        setUploadProgress(prev => ({ ...prev, [jenisberkas]: i }));\r\n      }\r\n\r\n      // Here you would make the actual API call\r\n      // const response = await api.uploadBerkas(formData);\r\n\r\n      setUploadedFiles(prev => ({\r\n        ...prev,\r\n        [jenisberkas]: {\r\n          name: file.name,\r\n          size: file.size,\r\n          uploadedAt: new Date().toISOString()\r\n        }\r\n      }));\r\n\r\n      setUploadProgress(prev => ({ ...prev, [jenisberkas]: null }));\r\n\r\n    } catch (error) {\r\n      console.error('Upload error:', error);\r\n      setUploadProgress(prev => ({ ...prev, [jenisberkas]: null }));\r\n    }\r\n  };\r\n\r\n  const FileUploadCard = ({ berkas }) => {\r\n    const isUploaded = uploadedFiles[berkas.key];\r\n    const progress = uploadProgress[berkas.key];\r\n\r\n    return (\r\n      <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\r\n        <div className=\"flex items-center justify-between mb-4\">\r\n          <div className=\"flex items-center\">\r\n            <span className=\"text-2xl mr-3\">{berkas.icon}</span>\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold text-gray-800\">{berkas.label}</h3>\r\n              <p className=\"text-sm text-gray-500\">\r\n                {isUploaded ? 'File telah diupload' : 'Belum ada file'}\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex items-center\">\r\n            {isUploaded ? (\r\n              <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">\r\n                ✓ Uploaded\r\n              </span>\r\n            ) : (\r\n              <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-600\">\r\n                Pending\r\n              </span>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {progress !== null && progress !== undefined ? (\r\n          <div className=\"mb-4\">\r\n            <div className=\"flex justify-between text-sm text-gray-600 mb-1\">\r\n              <span>Uploading...</span>\r\n              <span>{progress}%</span>\r\n            </div>\r\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n              <div\r\n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\r\n                style={{ width: `${progress}%` }}\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        ) : isUploaded ? (\r\n          <div className=\"mb-4 p-3 bg-green-50 border border-green-200 rounded-lg\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <p className=\"text-sm font-medium text-green-800\">{isUploaded.name}</p>\r\n                <p className=\"text-xs text-green-600\">\r\n                  {(isUploaded.size / 1024 / 1024).toFixed(2)} MB •\r\n                  Uploaded {new Date(isUploaded.uploadedAt).toLocaleDateString()}\r\n                </p>\r\n              </div>\r\n              <button className=\"text-green-600 hover:text-green-800\">\r\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\r\n                </svg>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        ) : null}\r\n\r\n        <div className=\"flex items-center justify-center w-full\">\r\n          <label className=\"flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100\">\r\n            <div className=\"flex flex-col items-center justify-center pt-5 pb-6\">\r\n              <svg className=\"w-8 h-8 mb-4 text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\r\n              </svg>\r\n              <p className=\"mb-2 text-sm text-gray-500\">\r\n                <span className=\"font-semibold\">Click to upload</span> or drag and drop\r\n              </p>\r\n              <p className=\"text-xs text-gray-500\">PDF, PNG, JPG or JPEG (MAX. 5MB)</p>\r\n            </div>\r\n            <input\r\n              type=\"file\"\r\n              className=\"hidden\"\r\n              accept=\".pdf,.png,.jpg,.jpeg\"\r\n              onChange={(e) => {\r\n                const file = e.target.files[0];\r\n                if (file) {\r\n                  handleFileUpload(file, berkas.key);\r\n                }\r\n              }}\r\n            />\r\n          </label>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <MainLayout>\r\n        <div className=\"flex justify-center items-center py-12\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\r\n          <span className=\"ml-3 text-gray-600\">Memuat data siswa...</span>\r\n        </div>\r\n      </MainLayout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"max-w-4xl mx-auto\">\r\n        {/* Header */}\r\n        <div className=\"mb-6\">\r\n          <Link to={`/siswa/${id}`} className=\"text-blue-600 hover:text-blue-800 text-sm mb-2 inline-block\">\r\n            ← Kembali ke Detail Siswa\r\n          </Link>\r\n          <h2 className=\"text-3xl font-bold text-gray-800\">Upload Berkas Siswa</h2>\r\n          {siswa && (\r\n            <p className=\"text-gray-600 mt-2\">\r\n              Upload berkas untuk <span className=\"font-semibold\">{siswa.nama_lengkap}</span> (NIS: {siswa.nis})\r\n            </p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Upload Cards Grid */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          {berkasTypes.map((berkas) => (\r\n            <FileUploadCard key={berkas.key} berkas={berkas} />\r\n          ))}\r\n        </div>\r\n\r\n        {/* Summary */}\r\n        <div className=\"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6\">\r\n          <h3 className=\"text-lg font-semibold text-blue-800 mb-2\">Informasi Upload</h3>\r\n          <ul className=\"text-sm text-blue-700 space-y-1\">\r\n            <li>• Maksimal ukuran file: 5MB per file</li>\r\n            <li>• Format yang didukung: PDF, PNG, JPG, JPEG</li>\r\n            <li>• Pastikan file dapat dibaca dengan jelas</li>\r\n            <li>• File yang diupload akan disimpan dengan aman</li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </MainLayout>\r\n  );\r\n};\r\n\r\nexport default UploadBerkasPage;", "import React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\r\nimport DashboardPage from './components/pages/DashboardPage';\r\nimport SiswaListPage from './components/pages/SiswaListPage';\r\nimport SiswaDetailPage from './components/pages/SiswaDetailPage';\r\nimport UploadBerkasPage from './components/pages/UploadBerkasPage';\r\n\r\nfunction App() {\r\n  return (\r\n    <Router>\r\n      <Routes>\r\n        <Route path=\"/\" element={<DashboardPage />} />\r\n        <Route path=\"/siswa\" element={<SiswaListPage />} />\r\n        <Route path=\"/siswa/:id\" element={<SiswaDetailPage />} />\r\n        <Route path=\"/upload-berkas/:id\" element={<UploadBerkasPage />} />\r\n      </Routes>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;", "import React from 'react';\r\nimport ReactDOM from 'react-dom/client';\r\nimport './index.css';\r\nimport App from './App';\r\n\r\nconst root = ReactDOM.createRoot(document.getElementById('root'));\r\nroot.render(\r\n  <React.StrictMode>\r\n    <App />\r\n  </React.StrictMode>\r\n);"], "names": ["f", "require$$0", "k", "l", "m", "n", "p", "q", "c", "g", "b", "d", "e", "h", "reactJsxRuntime_production_min", "jsxRuntimeModule", "MainLayout", "children", "location", "useLocation", "isActive", "path", "jsxs", "jsx", "Link", "DashboardPage", "apiClient", "axios", "config", "token", "error", "response", "_a", "api", "params", "id", "data", "riwayatId", "formData", "progressEvent", "percentCompleted", "berkasId", "query", "SiswaListPage", "siswaList", "setSiswaList", "useState", "loading", "setLoading", "setError", "useEffect", "res", "err", "siswa", "SiswaDetailPage", "useParams", "set<PERSON>is<PERSON>", "kelasHistory", "setKelasHistory", "siswaRes", "kelasRes", "item", "idx", "UploadBerkasPage", "uploadProgress", "setUploadProgress", "uploadedFiles", "setUploadedFiles", "berkasTypes", "handleFileUpload", "file", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prev", "i", "resolve", "FileUploadCard", "berkas", "isUploaded", "progress", "App", "Router", "Routes", "Route", "root", "ReactDOM", "React"], "mappings": ";;;;;;;;GASa,IAAIA,EAAEC,EAAiBC,EAAE,OAAO,IAAI,eAAe,EAAEC,EAAE,OAAO,IAAI,gBAAgB,EAAEC,EAAE,OAAO,UAAU,eAAeC,EAAEL,EAAE,mDAAmD,kBAAkBM,EAAE,CAAC,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,SAAS,EAAE,EAClP,SAASC,EAAEC,EAAE,EAAEC,EAAE,CAAC,IAAIC,EAAEC,EAAE,GAAGC,EAAE,KAAKC,EAAE,KAAcJ,IAAT,SAAaG,EAAE,GAAGH,GAAY,EAAE,MAAX,SAAiBG,EAAE,GAAG,EAAE,KAAc,EAAE,MAAX,SAAiBC,EAAE,EAAE,KAAK,IAAIH,KAAK,EAAEN,EAAE,KAAK,EAAEM,CAAC,GAAG,CAACJ,EAAE,eAAeI,CAAC,IAAIC,EAAED,CAAC,EAAE,EAAEA,CAAC,GAAG,GAAGF,GAAGA,EAAE,aAAa,IAAIE,KAAK,EAAEF,EAAE,aAAa,EAAWG,EAAED,CAAC,aAAIC,EAAED,CAAC,EAAE,EAAEA,CAAC,GAAG,MAAM,CAAC,SAASR,EAAE,KAAKM,EAAE,IAAII,EAAE,IAAIC,EAAE,MAAMF,EAAE,OAAON,EAAE,OAAO,CAAC,YAAkBF,EAAaW,EAAA,IAACP,EAAEO,EAAA,KAAaP,ECPjWQ,EAAA,QAAUd,uBCDfG,EAAIH,eAEeG,EAAE,yBACDA,EAAE,YCF1B,MAAMY,EAAa,CAAC,CAAE,SAAAC,KAAe,CACnC,MAAMC,EAAWC,EAAY,EAEvBC,EAAYC,GACTH,EAAS,WAAaG,EAI7B,OAAAC,EAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACC,EAAA,IAAA,SAAA,CAAO,UAAU,8CAChB,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,yCACb,SAAAD,OAAC,MAAI,CAAA,UAAU,yCACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,oBACb,SAAAA,EAAA,IAAC,MAAG,UAAU,mCAAmC,qCAEjD,CACF,CAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,GAAG,IACH,UAAW,8DACTJ,EAAS,GAAG,EACR,4BACA,qDACN,GACD,SAAA,WAAA,CAED,EACAG,EAAA,IAACC,EAAA,CACC,GAAG,SACH,UAAW,8DACTJ,EAAS,QAAQ,EACb,4BACA,qDACN,GACD,SAAA,YAAA,CAAA,CAED,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAECG,EAAAA,IAAA,OAAA,CAAK,UAAU,8CACb,SAAAN,CACH,CAAA,EAECM,MAAA,SAAA,CAAO,UAAU,4CAChB,eAAC,MAAI,CAAA,UAAU,8CACb,SAAAA,EAAA,IAAC,IAAE,CAAA,UAAU,oCAAoC,SAAA,sDAEjD,EACF,CACF,CAAA,CAAA,EACF,CAEJ,ECvDME,EAAgB,IAEjBF,EAAA,IAAAP,EAAA,CACC,SAACM,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAAS,YAAA,EAC/DD,EAAAA,KAAC,MAAI,CAAA,UAAU,yCACb,SAAA,CAACC,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAE1C,8CAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,oEAAA,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,qFACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAU,aAAA,EAClEA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAA8B,iCAAA,EAChEA,EAAA,IAACC,EAAA,CACC,GAAG,SACH,UAAU,4FACX,SAAA,oBAAA,CAAA,CAED,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,uFACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAAa,gBAAA,EACtEA,EAAA,IAAA,IAAA,CAAE,UAAU,sBAAsB,SAAgC,mCAAA,EACnEA,EAAA,IAACC,EAAA,CACC,GAAG,SACH,UAAU,8FACX,SAAA,eAAA,CAAA,CAED,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,yFACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,6CAA6C,SAAc,iBAAA,EACxEA,EAAA,IAAA,IAAA,CAAE,UAAU,uBAAuB,SAA+B,kCAAA,EACnEA,EAAA,IAACC,EAAA,CACC,GAAG,SACH,UAAU,gGACX,SAAA,eAAA,CAAA,CAED,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,ECnDEE,EAAYC,EAAM,OAAO,CAC7B,QAAS,4BACT,QAAS,IACT,QAAS,CACP,eAAgB,kBACjB,CACH,CAAC,EAGDD,EAAU,aAAa,QAAQ,IAC5BE,GAAW,CAEV,MAAMC,EAAQ,aAAa,QAAQ,WAAW,EAC9C,OAAIA,IACFD,EAAO,QAAQ,cAAgB,UAAUC,CAAK,IAEzCD,CACR,EACAE,GACQ,QAAQ,OAAOA,CAAK,CAE/B,EAGAJ,EAAU,aAAa,SAAS,IAC7BK,GACQA,EAERD,GAAU,OACT,QAAIE,EAAAF,EAAM,WAAN,YAAAE,EAAgB,UAAW,MAE7B,aAAa,WAAW,WAAW,EACnC,OAAO,SAAS,KAAO,UAElB,QAAQ,OAAOF,CAAK,CAC5B,CACH,EAEA,MAAeG,EAAA,CAEb,aAAaC,EAAS,GAAI,CACxB,OAAOR,EAAU,IAAI,SAAU,CAAE,OAAAQ,CAAQ,CAAA,CAC1C,EAED,aAAaC,EAAI,CACf,OAAOT,EAAU,IAAI,UAAUS,CAAE,EAAE,CACpC,EAED,YAAYC,EAAM,CAChB,OAAOV,EAAU,KAAK,SAAUU,CAAI,CACrC,EAED,YAAYD,EAAIC,EAAM,CACpB,OAAOV,EAAU,IAAI,UAAUS,CAAE,GAAIC,CAAI,CAC1C,EAED,YAAYD,EAAI,CACd,OAAOT,EAAU,OAAO,UAAUS,CAAE,EAAE,CACvC,EAGD,gBAAgBA,EAAI,CAClB,OAAOT,EAAU,IAAI,UAAUS,CAAE,gBAAgB,CAClD,EAED,gBAAgBA,EAAIC,EAAM,CACxB,OAAOV,EAAU,KAAK,UAAUS,CAAE,iBAAkBC,CAAI,CACzD,EAED,mBAAmBD,EAAIE,EAAWD,EAAM,CACtC,OAAOV,EAAU,IAAI,UAAUS,CAAE,kBAAkBE,CAAS,GAAID,CAAI,CACrE,EAGD,cAAcD,EAAI,CAChB,OAAOT,EAAU,IAAI,UAAUS,CAAE,SAAS,CAC3C,EAED,aAAaG,EAAU,CACrB,OAAOZ,EAAU,KAAK,iBAAkBY,EAAU,CAChD,QAAS,CACP,eAAgB,qBACjB,EACD,iBAAmBC,GAAkB,CACnC,MAAMC,EAAmB,KAAK,MAC3BD,EAAc,OAAS,IAAOA,EAAc,KACvD,EAEQ,QAAQ,IAAI,oBAAoBC,CAAgB,GAAG,CACpD,CACP,CAAK,CACF,EAED,aAAaC,EAAU,CACrB,OAAOf,EAAU,OAAO,WAAWe,CAAQ,EAAE,CAC9C,EAED,eAAeA,EAAU,CACvB,OAAOf,EAAU,IAAI,WAAWe,CAAQ,YAAa,CACnD,aAAc,MACpB,CAAK,CACF,EAGD,mBAAoB,CAClB,OAAOf,EAAU,IAAI,kBAAkB,CACxC,EAED,eAAgB,CACd,OAAOA,EAAU,IAAI,wBAAwB,CAC9C,EAGD,YAAYgB,EAAO,CACjB,OAAOhB,EAAU,IAAI,gBAAiB,CACpC,OAAQ,CAAE,EAAGgB,CAAO,CAC1B,CAAK,CACF,EAGD,aAAaN,EAAM,CACjB,OAAOV,EAAU,KAAK,UAAWU,CAAI,CACtC,EAED,cAAcF,EAAS,GAAI,CACzB,OAAOR,EAAU,IAAI,UAAW,CAAE,OAAAQ,CAAQ,CAAA,CAC3C,EAGD,gBAAgBE,EAAM,CACpB,OAAOV,EAAU,KAAK,aAAcU,CAAI,CACzC,EAED,iBAAiBF,EAAS,GAAI,CAC5B,OAAOR,EAAU,IAAI,aAAc,CAAE,OAAAQ,CAAQ,CAAA,CAC9C,CACH,ECrIMS,EAAgB,IAAM,CAC1B,KAAM,CAACC,EAAWC,CAAY,EAAIC,EAAAA,SAAS,CAAA,CAAE,EACvC,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAAChB,EAAOmB,CAAQ,EAAIH,EAAAA,SAAS,IAAI,EAmBvC,OAjBAI,EAAAA,UAAU,IAAM,EACK,SAAY,CACzB,GAAA,CACFF,EAAW,EAAI,EACfC,EAAS,IAAI,EACP,MAAAE,EAAM,MAAMlB,EAAI,aAAa,EACnCY,EAAaM,EAAI,KAAK,MAAQ,CAAA,CAAE,QACzBC,EAAK,CACJ,QAAA,MAAM,wBAAyBA,CAAG,EAC1CH,EAAS,6CAA6C,CAAA,QACtD,CACAD,EAAW,EAAK,CAAA,CAEpB,GACW,CACb,EAAG,EAAE,EAEDD,EAECxB,EAAA,IAAAP,EAAA,CACC,SAACM,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gEAAiE,CAAA,EAC/EA,EAAA,IAAA,OAAA,CAAK,UAAU,qBAAqB,SAAoB,sBAAA,CAAA,CAAA,CAAA,CAC3D,CACF,CAAA,EAIAO,EAEAP,EAAAA,IAACP,GACC,SAACO,EAAA,IAAA,MAAA,CAAI,UAAU,iDACb,SAAAD,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAAAA,MAAC,OAAI,UAAU,uBAAuB,QAAQ,YAAY,KAAK,eAC7D,SAACA,EAAA,IAAA,OAAA,CAAK,SAAS,UAAU,EAAE,0NAA0N,SAAS,UAAU,EAC1Q,CACF,CAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAK,QAAA,EACrDA,EAAA,IAAA,IAAA,CAAE,UAAU,4BAA6B,SAAMO,CAAA,CAAA,CAAA,CAClD,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAKDP,EAAA,IAAAP,EAAA,CACC,SAACM,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAY,eAAA,EAC5DA,EAAA,IAAA,SAAA,CAAO,UAAU,kFAAkF,SAEpG,gBAAA,CAAA,CAAA,EACF,EAECqB,EAAU,SAAW,EACnBtB,EAAA,KAAA,MAAA,CAAI,UAAU,+DACb,SAAA,CAACC,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAAoB,uBAAA,EACxDA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAAqC,uCAAA,CAAA,CAAA,CACzE,CAAA,EAEAA,EAAAA,IAAC,MAAI,CAAA,UAAU,gDACb,SAAAA,MAAC,MAAI,CAAA,UAAU,kBACb,SAAAD,EAAA,KAAC,QAAM,CAAA,UAAU,sCACf,SAAA,CAAAC,MAAC,QAAM,CAAA,UAAU,aACf,SAAAD,EAAA,KAAC,KACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iFAAiF,SAE/F,eAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,iFAAiF,SAE/F,MAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,iFAAiF,SAE/F,QAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,iFAAiF,SAE/F,MAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EACAA,EAAA,IAAC,QAAM,CAAA,UAAU,oCACd,SAAAqB,EAAU,IAAKS,GACd/B,EAAA,KAAC,KAAwB,CAAA,UAAU,mBACjC,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,8BACZ,SAAAA,EAAAA,IAAC,OAAI,UAAU,oCACZ,SAAM8B,EAAA,YAAA,CACT,CACF,CAAA,EACA9B,EAAA,IAAC,KAAG,CAAA,UAAU,8BACZ,SAAAA,EAAAA,IAAC,OAAI,UAAU,wBAAyB,SAAM8B,EAAA,GAAA,CAAI,CACpD,CAAA,EACA9B,EAAA,IAAC,KAAG,CAAA,UAAU,8BACZ,SAAAA,EAAAA,IAAC,OAAK,CAAA,UAAU,uFACb,SAAA8B,EAAM,YAAc,GACvB,CAAA,EACF,EACA/B,EAAAA,KAAC,KAAG,CAAA,UAAU,kDACZ,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,GAAI,UAAU6B,EAAM,QAAQ,GAC5B,UAAU,yCACX,SAAA,QAAA,CAED,EACA9B,EAAA,IAACC,EAAA,CACC,GAAI,kBAAkB6B,EAAM,QAAQ,GACpC,UAAU,sCACX,SAAA,eAAA,CAAA,CAED,CACF,CAAA,CAAA,GA3BOA,EAAM,QA4Bf,CACD,CACH,CAAA,CAAA,CACF,CAAA,CACF,CAAA,CACF,CAAA,CAAA,CAAA,CAEJ,CACF,CAAA,CAEJ,EChIMC,EAAkB,IAAM,CACtB,KAAA,CAAE,GAAAnB,CAAG,EAAIoB,EAAU,EACnB,CAACF,EAAOG,CAAQ,EAAIV,EAAAA,SAAS,IAAI,EACjC,CAACW,EAAcC,CAAe,EAAIZ,EAAAA,SAAS,CAAA,CAAE,EAC7C,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAAChB,EAAOmB,CAAQ,EAAIH,EAAAA,SAAS,IAAI,EAwBvC,OAtBAI,EAAAA,UAAU,IAAM,CAmBVf,IAlBc,SAAY,CACxB,GAAA,CACFa,EAAW,EAAI,EACfC,EAAS,IAAI,EACb,KAAM,CAACU,EAAUC,CAAQ,EAAI,MAAM,QAAQ,IAAI,CAC7C3B,EAAI,aAAaE,CAAE,EACnBF,EAAI,gBAAgBE,CAAE,CAAA,CACvB,EACQqB,EAAAG,EAAS,KAAK,IAAI,EAC3BD,EAAgBE,EAAS,KAAK,MAAQ,CAAA,CAAE,QACjCR,EAAK,CACJ,QAAA,MAAM,+BAAgCA,CAAG,EACjDH,EAAS,+CAA+C,CAAA,QACxD,CACAD,EAAW,EAAK,CAAA,CAEpB,GAEkB,CAAA,EACjB,CAACb,CAAE,CAAC,EAEHY,EAECxB,EAAA,IAAAP,EAAA,CACC,SAACM,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gEAAiE,CAAA,EAC/EA,EAAA,IAAA,OAAA,CAAK,UAAU,qBAAqB,SAAsB,wBAAA,CAAA,CAAA,CAAA,CAC7D,CACF,CAAA,EAIAO,EAEAP,EAAAA,IAACP,GACC,SAACO,EAAA,IAAA,MAAA,CAAI,UAAU,iDACb,SAAAD,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gBACb,SAAAA,MAAC,OAAI,UAAU,uBAAuB,QAAQ,YAAY,KAAK,eAC7D,SAACA,EAAA,IAAA,OAAA,CAAK,SAAS,UAAU,EAAE,0NAA0N,SAAS,UAAU,EAC1Q,CACF,CAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,OACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAK,QAAA,EACrDA,EAAA,IAAA,IAAA,CAAE,UAAU,4BAA6B,SAAMO,CAAA,CAAA,CAAA,CAClD,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,EAICuB,EAcF9B,EAAA,IAAAP,EAAA,CACC,SAACM,EAAAA,KAAA,MAAA,CAAI,UAAU,oBAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAC,MAACC,EAAK,CAAA,GAAG,SAAS,UAAU,8DAA8D,SAE1F,4BAAA,EACCD,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAoC,WAAM,YAAa,CAAA,CAAA,EACvE,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,GAAI,kBAAkBW,CAAE,GACxB,UAAU,oFACX,SAAA,eAAA,CAED,EACCZ,EAAA,IAAA,SAAA,CAAO,UAAU,kFAAkF,SAEpG,WAAA,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,gBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAe,kBAAA,EACxED,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,0CAA0C,SAAG,MAAA,EAC7DA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAyB,WAAM,GAAI,CAAA,CAAA,EAClD,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,0CAA0C,SAAY,eAAA,EACtEA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAyB,WAAM,YAAa,CAAA,CAAA,EAC3D,SACC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,0CAA0C,SAAU,aAAA,QACpE,IAAE,CAAA,UAAU,wBAAyB,SAAA8B,EAAM,YAAc,GAAI,CAAA,CAAA,EAChE,SACC,MACC,CAAA,SAAA,CAAC9B,EAAA,IAAA,QAAA,CAAM,UAAU,0CAA0C,SAAM,SAAA,EAChEA,EAAA,IAAA,OAAA,CAAK,UAAU,uFAAuF,SAEvG,OAAA,CAAA,CAAA,EACF,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,0CAA0C,SAAM,SAAA,QAChE,IAAE,CAAA,UAAU,wBAAyB,SAAA8B,EAAM,QAAU,aAAc,CAAA,CAAA,CACtE,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAGA/B,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAa,gBAAA,EACrEkC,EAAa,SAAW,EACvBlC,EAAAA,IAAC,MAAI,CAAA,UAAU,mBACb,SAAAA,EAAA,IAAC,IAAE,CAAA,UAAU,gBAAgB,SAAA,0BAAuB,EACtD,EAEAA,MAAC,MAAI,CAAA,UAAU,YACZ,SAAAkC,EAAa,IAAI,CAACI,EAAMC,IACtBvC,MAAA,MAAA,CAAc,UAAU,wCACvB,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,8BAA8B,SAAA,CAAA,mBACzBuC,EAAK,eAAA,EACxB,EACAvC,EAAAA,KAAC,IAAE,CAAA,UAAU,qBACV,SAAA,CAAKuC,EAAA,WAAW,MAAIA,EAAK,UAAA,EAC5B,EACCA,EAAK,SACJtC,EAAA,IAAC,KAAE,UAAU,6BAA8B,WAAK,OAAQ,CAAA,CAAA,EAE5D,EACCA,EAAA,IAAA,OAAA,CAAK,UAAW,4DACfsC,EAAK,YACD,8BACA,yBACN,GACG,SAAAA,EAAK,YAAc,aAAe,YACrC,CAAA,CAAA,EACF,CAAA,EApBQC,CAqBV,CACD,CACH,CAAA,CAAA,CAEJ,CAAA,CAAA,EACF,QAGC,MAAI,CAAA,UAAU,gBACb,SAACxC,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAU,aAAA,EACnED,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,GAAI,kBAAkBW,CAAE,GACxB,UAAU,6GACX,SAAA,eAAA,CAED,EACCZ,EAAA,IAAA,SAAA,CAAO,UAAU,2GAA2G,SAE7H,kBAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,+GAA+G,SAEjI,eAAA,EACCA,EAAA,IAAA,SAAA,CAAO,UAAU,+GAA+G,SAEjI,eAAA,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAjIGA,EAAA,IAAAP,EAAA,CACC,SAACM,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAgB,SAA0B,6BAAA,QACtDC,EAAK,CAAA,GAAG,SAAS,UAAU,sDAAsD,SAElF,yBAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CA4HN,ECjMMuC,EAAmB,IAAM,CACvB,KAAA,CAAE,GAAA5B,CAAG,EAAIoB,EAAU,EACnB,CAACF,EAAOG,CAAQ,EAAIV,EAAAA,SAAS,IAAI,EACjC,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAS,EAAI,EACrC,CAACkB,EAAgBC,CAAiB,EAAInB,EAAAA,SAAS,CAAA,CAAE,EACjD,CAACoB,EAAeC,CAAgB,EAAIrB,EAAAA,SAAS,CAAA,CAAE,EAE/CsB,EAAc,CAClB,CAAE,IAAK,iBAAkB,MAAO,iBAAkB,KAAM,aAAc,EACtE,CAAE,IAAK,aAAc,MAAO,aAAc,KAAM,IAAK,EACrD,CAAE,IAAK,QAAS,MAAO,QAAS,KAAM,IAAK,EAC3C,CAAE,IAAK,oBAAqB,MAAO,oBAAqB,KAAM,IAAK,EACnE,CAAE,IAAK,OAAQ,MAAO,aAAc,KAAM,IAAK,EAC/C,CAAE,IAAK,yBAA0B,MAAO,yBAA0B,KAAM,IAAK,CAC/E,EAEAlB,EAAAA,UAAU,IAAM,CAYVf,IAXe,SAAY,CACzB,GAAA,CACF,MAAMgB,EAAM,MAAMlB,EAAI,aAAaE,CAAE,EAC5BqB,EAAAL,EAAI,KAAK,IAAI,QACfC,EAAK,CACJ,QAAA,MAAM,wBAAyBA,CAAG,CAAA,QAC1C,CACAJ,EAAW,EAAK,CAAA,CAEpB,GAEmB,CAAA,EAClB,CAACb,CAAE,CAAC,EAED,MAAAkC,EAAmB,MAAOC,EAAMC,IAAgB,CACpD,GAAI,CAACD,EAAM,OAGOL,EAAAO,IAAS,CAAE,GAAGA,EAAM,CAACD,CAAW,EAAG,GAAI,EAEnD,MAAAjC,EAAW,IAAI,SACZA,EAAA,OAAO,OAAQgC,CAAI,EACnBhC,EAAA,OAAO,WAAYH,CAAE,EACrBG,EAAA,OAAO,eAAgBiC,CAAW,EAEvC,GAAA,CAEF,QAASE,EAAI,EAAGA,GAAK,IAAKA,GAAK,GAC7B,MAAM,IAAI,QAAQC,GAAW,WAAWA,EAAS,GAAG,CAAC,EACnCT,EAAAO,IAAS,CAAE,GAAGA,EAAM,CAACD,CAAW,EAAGE,GAAI,EAM3DN,EAA0BK,IAAA,CACxB,GAAGA,EACH,CAACD,CAAW,EAAG,CACb,KAAMD,EAAK,KACX,KAAMA,EAAK,KACX,WAAY,IAAI,KAAK,EAAE,YAAY,CAAA,CACrC,EACA,EAEgBL,EAAAO,IAAS,CAAE,GAAGA,EAAM,CAACD,CAAW,EAAG,MAAO,QAErDzC,EAAO,CACN,QAAA,MAAM,gBAAiBA,CAAK,EAClBmC,EAAAO,IAAS,CAAE,GAAGA,EAAM,CAACD,CAAW,EAAG,MAAO,CAAA,CAEhE,EAEMI,EAAiB,CAAC,CAAE,OAAAC,KAAa,CAC/B,MAAAC,EAAaX,EAAcU,EAAO,GAAG,EACrCE,EAAWd,EAAeY,EAAO,GAAG,EAGxC,OAAAtD,EAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAAAC,EAAA,IAAC,OAAK,CAAA,UAAU,gBAAiB,SAAAqD,EAAO,KAAK,SAC5C,MACC,CAAA,SAAA,CAAArD,EAAA,IAAC,KAAG,CAAA,UAAU,sCAAuC,SAAAqD,EAAO,MAAM,QACjE,IAAE,CAAA,UAAU,wBACV,SAAAC,EAAa,sBAAwB,gBACxC,CAAA,CAAA,CACF,CAAA,CAAA,EACF,QACC,MAAI,CAAA,UAAU,oBACZ,SAAAA,QACE,OAAK,CAAA,UAAU,uFAAuF,SAAA,YAAA,CAEvG,EAECtD,EAAAA,IAAA,OAAA,CAAK,UAAU,qFAAqF,mBAErG,CAEJ,CAAA,CAAA,EACF,EAECuD,GAAa,KACXxD,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,kDACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAY,cAAA,CAAA,SACjB,OAAM,CAAA,SAAA,CAAAuD,EAAS,GAAA,CAAC,CAAA,CAAA,EACnB,EACAvD,EAAAA,IAAC,MAAI,CAAA,UAAU,sCACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,2DACV,MAAO,CAAE,MAAO,GAAGuD,CAAQ,GAAI,CAAA,CAAA,CAEnC,CAAA,CACF,CAAA,CAAA,EACED,EACDtD,EAAA,IAAA,MAAA,CAAI,UAAU,0DACb,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAC,EAAA,IAAC,IAAE,CAAA,UAAU,qCAAsC,SAAAsD,EAAW,KAAK,EACnEvD,EAAAA,KAAC,IAAE,CAAA,UAAU,yBACT,SAAA,EAAAuD,EAAW,KAAO,KAAO,MAAM,QAAQ,CAAC,EAAE,kBAClC,IAAI,KAAKA,EAAW,UAAU,EAAE,mBAAmB,CAAA,CAC/D,CAAA,CAAA,EACF,EACCtD,EAAA,IAAA,SAAA,CAAO,UAAU,sCAChB,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,UAAU,KAAK,OAAO,OAAO,eAAe,QAAQ,YACjE,SAAA,CAACC,EAAAA,IAAA,OAAA,CAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,kCAAmC,CAAA,EACxGA,EAAAA,IAAC,QAAK,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,yHAA0H,CAAA,CAAA,CAAA,CACjM,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACE,WAEH,MAAI,CAAA,UAAU,0CACb,SAACD,EAAA,KAAA,QAAA,CAAM,UAAU,sJACf,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,sDACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,6BAA6B,KAAK,OAAO,OAAO,eAAe,QAAQ,YACpF,eAAC,OAAK,CAAA,cAAc,QAAQ,eAAe,QAAQ,YAAa,EAAG,EAAE,wFAAwF,CAC/J,CAAA,EACAD,EAAAA,KAAC,IAAE,CAAA,UAAU,6BACX,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAe,kBAAA,EAAO,mBAAA,EACxD,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAAgC,kCAAA,CAAA,CAAA,EACvE,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,UAAU,SACV,OAAO,uBACP,SAAWX,GAAM,CACf,MAAM0D,EAAO1D,EAAE,OAAO,MAAM,CAAC,EACzB0D,GACeD,EAAAC,EAAMM,EAAO,GAAG,CACnC,CACF,CAAA,CACF,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAEJ,EAEA,OAAI7B,EAECxB,EAAA,IAAAP,EAAA,CACC,SAACM,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,gEAAiE,CAAA,EAC/EA,EAAA,IAAA,OAAA,CAAK,UAAU,qBAAqB,SAAoB,sBAAA,CAAA,CAAA,CAAA,CAC3D,CACF,CAAA,EAKDA,EAAA,IAAAP,EAAA,CACC,SAACM,EAAAA,KAAA,MAAA,CAAI,UAAU,oBAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAAAC,EAAAA,IAACC,GAAK,GAAI,UAAUW,CAAE,GAAI,UAAU,8DAA8D,SAElG,2BAAA,CAAA,EACCZ,EAAA,IAAA,KAAA,CAAG,UAAU,mCAAmC,SAAmB,sBAAA,EACnE8B,GACC/B,EAAA,KAAC,IAAE,CAAA,UAAU,qBAAqB,SAAA,CAAA,uBACXC,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAiB,WAAM,aAAa,EAAO,UAAQ8B,EAAM,IAAI,GAAA,CACnG,CAAA,CAAA,EAEJ,EAGC9B,EAAA,IAAA,MAAA,CAAI,UAAU,wCACZ,WAAY,IAAKqD,GAChBrD,EAAA,IAACoD,EAAgC,CAAA,OAAAC,CAAA,EAAZA,EAAO,GAAqB,CAClD,EACH,EAGAtD,EAAAA,KAAC,MAAI,CAAA,UAAU,wDACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAgB,mBAAA,EACzED,EAAAA,KAAC,KAAG,CAAA,UAAU,kCACZ,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAAoC,sCAAA,CAAA,EACxCA,EAAAA,IAAC,MAAG,SAA2C,6CAAA,CAAA,EAC/CA,EAAAA,IAAC,MAAG,SAAyC,2CAAA,CAAA,EAC7CA,EAAAA,IAAC,MAAG,SAA8C,gDAAA,CAAA,CAAA,CACpD,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EC5MA,SAASwD,GAAM,CAEX,OAAAxD,EAAAA,IAACyD,EACC,CAAA,SAAA1D,EAAAA,KAAC2D,EACC,CAAA,SAAA,CAAA1D,MAAC2D,GAAM,KAAK,IAAI,QAAS3D,MAACE,GAAc,CAAA,EAAI,QAC3CyD,EAAM,CAAA,KAAK,SAAS,QAAS3D,MAACoB,GAAc,CAAA,EAAI,QAChDuC,EAAM,CAAA,KAAK,aAAa,QAAS3D,MAAC+B,GAAgB,CAAA,EAAI,QACtD4B,EAAM,CAAA,KAAK,qBAAqB,QAAS3D,EAAA,IAACwC,IAAiB,CAAI,CAAA,CAAA,CAAA,CAClE,CACF,CAAA,CAEJ,CCbA,MAAMoB,EAAOC,EAAS,WAAW,SAAS,eAAe,MAAM,CAAC,EAChED,EAAK,aACFE,EAAM,WAAN,CACC,SAAA9D,MAACwD,IAAI,CACP,CAAA,CACF", "x_google_ignoreList": [0, 1, 2]}