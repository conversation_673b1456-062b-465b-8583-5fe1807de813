import{r as c,a as B,b as D}from"./vendor-Gm9i_4Ku.js";import{u as E,L as h,a as _,B as $,R as K,b as w}from"./router-DBIF4lX1.js";import{a as M}from"./utils-xsH4HHeE.js";(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const t of document.querySelectorAll('link[rel="modulepreload"]'))r(t);new MutationObserver(t=>{for(const l of t)if(l.type==="childList")for(const i of l.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function d(t){const l={};return t.integrity&&(l.integrity=t.integrity),t.referrerPolicy&&(l.referrerPolicy=t.referrerPolicy),t.crossOrigin==="use-credentials"?l.credentials="include":t.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(t){if(t.ep)return;t.ep=!0;const l=d(t);fetch(t.href,l)}})();var P={exports:{}},v={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var U=c,I=Symbol.for("react.element"),A=Symbol.for("react.fragment"),C=Object.prototype.hasOwnProperty,F=U.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,O={key:!0,ref:!0,__self:!0,__source:!0};function R(s,a,d){var r,t={},l=null,i=null;d!==void 0&&(l=""+d),a.key!==void 0&&(l=""+a.key),a.ref!==void 0&&(i=a.ref);for(r in a)C.call(a,r)&&!O.hasOwnProperty(r)&&(t[r]=a[r]);if(s&&s.defaultProps)for(r in a=s.defaultProps,a)t[r]===void 0&&(t[r]=a[r]);return{$$typeof:I,type:s,key:l,ref:i,props:t,_owner:F.current}}v.Fragment=A;v.jsx=R;v.jsxs=R;P.exports=v;var e=P.exports,S={},L=B;S.createRoot=L.createRoot,S.hydrateRoot=L.hydrateRoot;const p=({children:s})=>{const a=E(),d=r=>a.pathname===r;return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsx("div",{className:"flex items-center",children:e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Sistem Informasi Akademik"})}),e.jsxs("nav",{className:"flex space-x-8",children:[e.jsx(h,{to:"/",className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${d("/")?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:"Dashboard"}),e.jsx(h,{to:"/siswa",className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${d("/siswa")?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:"Data Siswa"})]})]})})}),e.jsx("main",{className:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8",children:s}),e.jsx("footer",{className:"bg-white border-t border-gray-200 mt-auto",children:e.jsx("div",{className:"max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8",children:e.jsx("p",{className:"text-center text-sm text-gray-500",children:"© 2025 Sistem Informasi Siswa. All rights reserved."})})})]})},T=()=>e.jsx(p,{children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-6",children:"Dashboard"}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[e.jsx("p",{className:"text-lg text-gray-600 mb-4",children:"Selamat datang di Sistem Informasi Akademik"}),e.jsx("p",{className:"text-gray-500 mb-6",children:"Kelola data siswa, riwayat kelas, dan berkas digital dengan mudah."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsx("h3",{className:"text-xl font-semibold text-blue-800 mb-3",children:"Data Siswa"}),e.jsx("p",{className:"text-blue-600 mb-4",children:"Kelola informasi lengkap siswa"}),e.jsx(h,{to:"/siswa",className:"inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors",children:"Lihat Daftar Siswa"})]}),e.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsx("h3",{className:"text-xl font-semibold text-green-800 mb-3",children:"Riwayat Kelas"}),e.jsx("p",{className:"text-green-600 mb-4",children:"Pantau perjalanan akademik siswa"}),e.jsx(h,{to:"/siswa",className:"inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors",children:"Lihat Riwayat"})]}),e.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsx("h3",{className:"text-xl font-semibold text-purple-800 mb-3",children:"Berkas Digital"}),e.jsx("p",{className:"text-purple-600 mb-4",children:"Upload dan kelola dokumen siswa"}),e.jsx(h,{to:"/siswa",className:"inline-block bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors",children:"Kelola Berkas"})]})]})]})}),n=M.create({baseURL:"http://localhost:5000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});n.interceptors.request.use(s=>{const a=localStorage.getItem("authToken");return a&&(s.headers.Authorization=`Bearer ${a}`),s},s=>Promise.reject(s));n.interceptors.response.use(s=>s,s=>{var a;return((a=s.response)==null?void 0:a.status)===401&&(localStorage.removeItem("authToken"),window.location.href="/login"),Promise.reject(s)});const N={getSiswaList(s={}){return n.get("/siswa",{params:s})},getSiswaById(s){return n.get(`/siswa/${s}`)},createSiswa(s){return n.post("/siswa",s)},updateSiswa(s,a){return n.put(`/siswa/${s}`,a)},deleteSiswa(s){return n.delete(`/siswa/${s}`)},getRiwayatKelas(s){return n.get(`/siswa/${s}/riwayat_kelas`)},addRiwayatKelas(s,a){return n.post(`/siswa/${s}/riwayat_kelas`,a)},updateRiwayatKelas(s,a,d){return n.put(`/siswa/${s}/riwayat_kelas/${a}`,d)},getBerkasList(s){return n.get(`/siswa/${s}/berkas`)},uploadBerkas(s){return n.post("/berkas/upload",s,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:a=>{const d=Math.round(a.loaded*100/a.total);console.log(`Upload Progress: ${d}%`)}})},deleteBerkas(s){return n.delete(`/berkas/${s}`)},downloadBerkas(s){return n.get(`/berkas/${s}/download`,{responseType:"blob"})},getDashboardStats(){return n.get("/dashboard/stats")},getKelasStats(){return n.get("/dashboard/kelas-stats")},searchSiswa(s){return n.get("/siswa/search",{params:{q:s}})},createMutasi(s){return n.post("/mutasi",s)},getMutasiList(s={}){return n.get("/mutasi",{params:s})},createKelulusan(s){return n.post("/kelulusan",s)},getKelulusanList(s={}){return n.get("/kelulusan",{params:s})}},z=()=>{const[s,a]=c.useState([]),[d,r]=c.useState(!0),[t,l]=c.useState(null);return c.useEffect(()=>{(async()=>{try{r(!0),l(null);const g=await N.getSiswaList();a(g.data.data||[])}catch(g){console.error("Error fetching siswa:",g),l("Gagal memuat data siswa. Silakan coba lagi.")}finally{r(!1)}})()},[]),d?e.jsx(p,{children:e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"}),e.jsx("span",{className:"ml-3 text-gray-600",children:"Memuat data siswa..."})]})}):t?e.jsx(p,{children:e.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),e.jsx("p",{className:"text-sm text-red-700 mt-1",children:t})]})]})})}):e.jsx(p,{children:e.jsxs("div",{className:"max-w-6xl mx-auto",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-3xl font-bold text-gray-800",children:"Daftar Siswa"}),e.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"+ Tambah Siswa"})]}),s.length===0?e.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-8 text-center",children:[e.jsx("p",{className:"text-gray-500 text-lg",children:"Belum ada data siswa"}),e.jsx("p",{className:"text-gray-400 mt-2",children:"Tambahkan siswa pertama untuk memulai"})]}):e.jsx("div",{className:"bg-white shadow-md rounded-lg overflow-hidden",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Nama Lengkap"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"NIS"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kelas"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Aksi"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(i=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:i.nama_lengkap})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:i.nis})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:i.kelas_awal||"-"})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[e.jsx(h,{to:`/siswa/${i.id_siswa}`,className:"text-blue-600 hover:text-blue-900 mr-4",children:"Detail"}),e.jsx(h,{to:`/upload-berkas/${i.id_siswa}`,className:"text-green-600 hover:text-green-900",children:"Upload Berkas"})]})]},i.id_siswa))})]})})})]})})},G=()=>{const{id:s}=_(),[a,d]=c.useState(null),[r,t]=c.useState([]),[l,i]=c.useState(!0),[g,y]=c.useState(null);return c.useEffect(()=>{s&&(async()=>{try{i(!0),y(null);const[f,k]=await Promise.all([N.getSiswaById(s),N.getRiwayatKelas(s)]);d(f.data.data),t(k.data.data||[])}catch(f){console.error("Error fetching siswa detail:",f),y("Gagal memuat detail siswa. Silakan coba lagi.")}finally{i(!1)}})()},[s]),l?e.jsx(p,{children:e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"}),e.jsx("span",{className:"ml-3 text-gray-600",children:"Memuat detail siswa..."})]})}):g?e.jsx(p,{children:e.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),e.jsx("p",{className:"text-sm text-red-700 mt-1",children:g})]})]})})}):a?e.jsx(p,{children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{children:[e.jsx(h,{to:"/siswa",className:"text-blue-600 hover:text-blue-800 text-sm mb-2 inline-block",children:"← Kembali ke Daftar Siswa"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800",children:a.nama_lengkap})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(h,{to:`/upload-berkas/${s}`,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors",children:"Upload Berkas"}),e.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Edit Data"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Informasi Siswa"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-500",children:"NIS"}),e.jsx("p",{className:"text-lg text-gray-900",children:a.nis})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-500",children:"Nama Lengkap"}),e.jsx("p",{className:"text-lg text-gray-900",children:a.nama_lengkap})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-500",children:"Kelas Awal"}),e.jsx("p",{className:"text-lg text-gray-900",children:a.kelas_awal||"-"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-500",children:"Status"}),e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:"Aktif"})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-500",children:"Alamat"}),e.jsx("p",{className:"text-lg text-gray-900",children:a.alamat||"Belum diisi"})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-800 mb-4",children:"Riwayat Kelas"}),r.length===0?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-500",children:"Belum ada riwayat kelas"})}):e.jsx("div",{className:"space-y-4",children:r.map((u,f)=>e.jsx("div",{className:"border border-gray-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"font-semibold text-gray-800",children:["Tahun Pelajaran ",u.tahun_pelajaran]}),e.jsxs("p",{className:"text-gray-600 mt-1",children:[u.kelas_awal," → ",u.kelas_naik]}),u.catatan&&e.jsx("p",{className:"text-sm text-gray-500 mt-2",children:u.catatan})]}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${u.status_naik?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:u.status_naik?"Naik Kelas":"Tidak Naik"})]})},f))})]})]}),e.jsx("div",{className:"lg:col-span-1",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Aksi Cepat"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(h,{to:`/upload-berkas/${s}`,className:"block w-full text-center bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors",children:"Upload Berkas"}),e.jsx("button",{className:"block w-full text-center bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Edit Data Siswa"}),e.jsx("button",{className:"block w-full text-center bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors",children:"Lihat Berkas"}),e.jsx("button",{className:"block w-full text-center bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors",children:"Proses Mutasi"})]})]})})]})]})}):e.jsx(p,{children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("p",{className:"text-gray-500",children:"Data siswa tidak ditemukan"}),e.jsx(h,{to:"/siswa",className:"text-blue-600 hover:text-blue-800 mt-2 inline-block",children:"Kembali ke Daftar Siswa"})]})})},J=()=>{const{id:s}=_(),[a,d]=c.useState(null),[r,t]=c.useState(!0),[l,i]=c.useState({}),[g,y]=c.useState({}),u=[{key:"kartu_keluarga",label:"Kartu Keluarga",icon:"👨‍👩‍👧‍👦"},{key:"akta_lahir",label:"Akta Lahir",icon:"📄"},{key:"rapor",label:"Rapor",icon:"📊"},{key:"ijazah_sebelumnya",label:"Ijazah Sebelumnya",icon:"🎓"},{key:"foto",label:"Foto Siswa",icon:"📸"},{key:"surat_keterangan_sehat",label:"Surat Keterangan Sehat",icon:"🏥"}];c.useEffect(()=>{s&&(async()=>{try{const o=await N.getSiswaById(s);d(o.data.data)}catch(o){console.error("Error fetching siswa:",o)}finally{t(!1)}})()},[s]);const f=async(x,o)=>{if(!x)return;i(m=>({...m,[o]:0}));const b=new FormData;b.append("file",x),b.append("id_siswa",s),b.append("jenis_berkas",o);try{for(let m=0;m<=100;m+=10)await new Promise(j=>setTimeout(j,100)),i(j=>({...j,[o]:m}));y(m=>({...m,[o]:{name:x.name,size:x.size,uploadedAt:new Date().toISOString()}})),i(m=>({...m,[o]:null}))}catch(m){console.error("Upload error:",m),i(j=>({...j,[o]:null}))}},k=({berkas:x})=>{const o=g[x.key],b=l[x.key];return e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-2xl mr-3",children:x.icon}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:x.label}),e.jsx("p",{className:"text-sm text-gray-500",children:o?"File telah diupload":"Belum ada file"})]})]}),e.jsx("div",{className:"flex items-center",children:o?e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:"✓ Uploaded"}):e.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-600",children:"Pending"})})]}),b!=null?e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex justify-between text-sm text-gray-600 mb-1",children:[e.jsx("span",{children:"Uploading..."}),e.jsxs("span",{children:[b,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${b}%`}})})]}):o?e.jsx("div",{className:"mb-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-green-800",children:o.name}),e.jsxs("p",{className:"text-xs text-green-600",children:[(o.size/1024/1024).toFixed(2)," MB • Uploaded ",new Date(o.uploadedAt).toLocaleDateString()]})]}),e.jsx("button",{className:"text-green-600 hover:text-green-800",children:e.jsxs("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]})}):null,e.jsx("div",{className:"flex items-center justify-center w-full",children:e.jsxs("label",{className:"flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center pt-5 pb-6",children:[e.jsx("svg",{className:"w-8 h-8 mb-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),e.jsxs("p",{className:"mb-2 text-sm text-gray-500",children:[e.jsx("span",{className:"font-semibold",children:"Click to upload"})," or drag and drop"]}),e.jsx("p",{className:"text-xs text-gray-500",children:"PDF, PNG, JPG or JPEG (MAX. 5MB)"})]}),e.jsx("input",{type:"file",className:"hidden",accept:".pdf,.png,.jpg,.jpeg",onChange:m=>{const j=m.target.files[0];j&&f(j,x.key)}})]})})]})};return r?e.jsx(p,{children:e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"}),e.jsx("span",{className:"ml-3 text-gray-600",children:"Memuat data siswa..."})]})}):e.jsx(p,{children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx(h,{to:`/siswa/${s}`,className:"text-blue-600 hover:text-blue-800 text-sm mb-2 inline-block",children:"← Kembali ke Detail Siswa"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800",children:"Upload Berkas Siswa"}),a&&e.jsxs("p",{className:"text-gray-600 mt-2",children:["Upload berkas untuk ",e.jsx("span",{className:"font-semibold",children:a.nama_lengkap})," (NIS: ",a.nis,")"]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:u.map(x=>e.jsx(k,{berkas:x},x.key))}),e.jsxs("div",{className:"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-blue-800 mb-2",children:"Informasi Upload"}),e.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[e.jsx("li",{children:"• Maksimal ukuran file: 5MB per file"}),e.jsx("li",{children:"• Format yang didukung: PDF, PNG, JPG, JPEG"}),e.jsx("li",{children:"• Pastikan file dapat dibaca dengan jelas"}),e.jsx("li",{children:"• File yang diupload akan disimpan dengan aman"})]})]})]})})};function q(){return e.jsx($,{children:e.jsxs(K,{children:[e.jsx(w,{path:"/",element:e.jsx(T,{})}),e.jsx(w,{path:"/siswa",element:e.jsx(z,{})}),e.jsx(w,{path:"/siswa/:id",element:e.jsx(G,{})}),e.jsx(w,{path:"/upload-berkas/:id",element:e.jsx(J,{})})]})})}const W=S.createRoot(document.getElementById("root"));W.render(e.jsx(D.StrictMode,{children:e.jsx(q,{})}));
//# sourceMappingURL=index-CzuBd4z7.js.map
