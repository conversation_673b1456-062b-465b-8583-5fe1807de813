import React, { useState } from 'react';
import api from '../services/api';

const UploadForm = ({ id }) => {
  const [file, setFile] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append('kartu_keluarga', file);

    try {
      await api.uploadBerkas(id, formData);
      alert('Berkas berhasil diupload!');
    } catch (err) {
      alert('Gagal upload berkas.');
      console.error(err);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input type="file" onChange={(e) => setFile(e.target.files[0])} required />
      <button type="submit">Upload Berkas</button>
    </form>
  );
};

export default UploadForm;