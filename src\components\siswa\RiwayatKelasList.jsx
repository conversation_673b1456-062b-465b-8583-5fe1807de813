import React, { useEffect, useState } from 'react';
import axios from 'axios';

/**
 * Komponen RiwayatKelasList - Menampilkan riwayat naik kelas seorang siswa
 * @param {string} id_siswa - ID siswa untuk mengambil data riwayat kelas
 */
const RiwayatKelasList = ({ id_siswa }) => {
  const [riwayatList, setRiwayatList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchRiwayatKelas = async () => {
      try {
        const response = await axios.get(`http://localhost:5000/api/siswa/${id_siswa}/riwayat_kelas`);

        if (response.status === 200) {
          setRiwayatList(response.data.data);
        }
      } catch (err) {
        console.error('Gagal memuat riwayat kelas:', err);
        setError('Gagal memuat riwayat kenaikan kelas.');
      } finally {
        setLoading(false);
      }
    };

    if (id_siswa) {
      fetchRiwayatKelas();
    }
  }, [id_siswa]);

  if (loading) {
    return <p>Memuat riwayat kelas...</p>;
  }

  if (error) {
    return <p className="text-red-600">{error}</p>;
  }

  return (
    <div className="mb-6">
      <h3 className="text-xl font-semibold mb-4">Riwayat Kelas</h3>

      {riwayatList.length === 0 ? (
        <p>Tidak ada riwayat kenaikan kelas.</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-300 rounded-lg shadow-sm">
            <thead className="bg-gray-100">
              <tr>
                <th className="py-2 px-4 border-b">Tahun Pelajaran</th>
                <th className="py-2 px-4 border-b">Dari Kelas</th>
                <th className="py-2 px-4 border-b">Naik ke Kelas</th>
                <th className="py-2 px-4 border-b">Status</th>
                <th className="py-2 px-4 border-b">Catatan</th>
              </tr>
            </thead>
            <tbody>
              {riwayatList.map((item, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="py-2 px-4 border-b">{item.tahun_pelajaran}</td>
                  <td className="py-2 px-4 border-b">{item.kelas_awal}</td>
                  <td className="py-2 px-4 border-b">{item.kelas_naik}</td>
                  <td className="py-2 px-4 border-b">
                    <span
                      className={`${
                        item.status_naik ? 'text-green-600' : 'text-red-600'
                      } font-medium`}
                    >
                      {item.status_naik ? 'Naik' : 'Tidak Naik'}
                    </span>
                  </td>
                  <td className="py-2 px-4 border-b">{item.catatan || '-'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default RiwayatKelasList;