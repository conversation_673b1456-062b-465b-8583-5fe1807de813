import React from 'react';

/**
 * Komponen DataCard - Menampilkan data dalam format kartu
 * @param {string} title - Judul kartu
 * @param {React.ReactNode} children - Konten utama kartu
 * @param {string} [className] - <PERSON><PERSON> ta<PERSON> untuk styling
 */
const DataCard = ({ title, children, className = '' }) => {
  return (
    <div className={`bg-white shadow-md rounded-lg p-4 mb-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-800 border-b pb-2 mb-4">
        {title}
      </h3>
      <div className="space-y-2">
        {children}
      </div>
    </div>
  );
};

export default DataCard;