import { useEffect, useState } from 'react';

/**
 * Custom Hook untuk Fetching Data dari API
 * @param {string} url - URL endpoint API
 * @param {object} options - <PERSON><PERSON> tambahan (method, headers, body, dll.)
 * @returns {{
 *   data: any,
 *   loading: boolean,
 *   error: string | null,
 *   refetch: () => void
 * }}
 */
const useFetch = (url, options = {}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refetchCount, setRefetchCount] = useState(0);

  const refetch = () => {
    setRefetchCount((prev) => prev + 1);
  };

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(url, {
          ...options,
          headers: {
            'Content-Type': 'application/json',
            ...(options.headers || {}),
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (isMounted) {
          setData(result.data || result);
        }
      } catch (err) {
        if (isMounted) {
          setError(err.message || 'Terjadi kesalahan saat mengambil data.');
        }
        console.error('Fetch error:', err);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [url, options, refetchCount]);

  return { data, loading, error, refetch };
};

export default useFetch;